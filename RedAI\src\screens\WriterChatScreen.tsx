import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  Alert,
  Text,
  TouchableOpacity,
  Modal,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';
import uuid from 'react-native-uuid';

// استيراد المكونات والخدمات
import ChatMessage from '../components/ChatMessage';
import ChatInput from '../components/ChatInput';
import { useApp } from '../context/AppContext';
import { Message, ChatSession } from '../types';
import geminiService from '../services/geminiService';
import i18nService from '../services/i18nService';

const WriterChatScreen: React.FC = () => {
  const { t } = useTranslation();
  const { state, dispatch } = useApp();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  const [showStyleModal, setShowStyleModal] = useState(false);
  const [selectedStyle, setSelectedStyle] = useState<string>('عام');
  const flatListRef = useRef<FlatList>(null);
  const isRTL = i18nService.isRTL();

  // أنماط الكتابة المتاحة
  const writingStyles = [
    { id: 'general', name: 'عام', icon: 'edit' },
    { id: 'story', name: 'قصة', icon: 'book' },
    { id: 'article', name: 'مقال', icon: 'article' },
    { id: 'summary', name: 'تلخيص', icon: 'summarize' },
    { id: 'creative', name: 'إبداعي', icon: 'lightbulb' },
    { id: 'formal', name: 'رسمي', icon: 'business' },
  ];

  // إنشاء جلسة جديدة أو تحميل الجلسة الحالية
  useEffect(() => {
    const existingSession = state.chatSessions.find(
      session => session.type === 'writer'
    );

    if (existingSession) {
      setCurrentSessionId(existingSession.id);
      setMessages(existingSession.messages);
    } else {
      createNewSession();
    }
  }, []);

  // إنشاء جلسة دردشة جديدة
  const createNewSession = () => {
    const sessionId = uuid.v4() as string;
    const newSession: ChatSession = {
      id: sessionId,
      title: t('chat.writer.title'),
      messages: [
        {
          id: uuid.v4() as string,
          text: t('chat.writer.emptyState'),
          isUser: false,
          timestamp: new Date(),
          type: 'text',
        }
      ],
      type: 'writer',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    dispatch({ type: 'CREATE_SESSION', payload: newSession });
    setCurrentSessionId(sessionId);
    setMessages(newSession.messages);
  };

  // إرسال رسالة
  const handleSendMessage = async (messageText: string) => {
    if (!messageText.trim()) return;

    const userMessage: Message = {
      id: uuid.v4() as string,
      text: messageText,
      isUser: true,
      timestamp: new Date(),
      type: 'text',
    };

    // إضافة رسالة المستخدم
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    
    // إضافة الرسالة إلى الجلسة
    dispatch({
      type: 'ADD_MESSAGE',
      payload: { sessionId: currentSessionId, message: userMessage },
    });

    setIsLoading(true);

    try {
      // الحصول على رد من Gemini للكتاب
      const aiResponse = await geminiService.generateWriterResponse(
        messageText,
        selectedStyle
      );

      const aiMessage: Message = {
        id: uuid.v4() as string,
        text: aiResponse,
        isUser: false,
        timestamp: new Date(),
        type: 'text',
      };

      // إضافة رد الذكاء الاصطناعي
      const finalMessages = [...updatedMessages, aiMessage];
      setMessages(finalMessages);

      // إضافة الرسالة إلى الجلسة
      dispatch({
        type: 'ADD_MESSAGE',
        payload: { sessionId: currentSessionId, message: aiMessage },
      });

    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert(
        t('common.error'),
        error instanceof Error ? error.message : t('errors.generalError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  // مسح المحادثة
  const handleClearChat = () => {
    Alert.alert(
      t('chat.clearChat'),
      'هل أنت متأكد من مسح هذه المحادثة؟',
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => {
            dispatch({ type: 'DELETE_SESSION', payload: currentSessionId });
            createNewSession();
          },
        },
      ]
    );
  };

  // تغيير نمط الكتابة
  const handleStyleChange = (style: any) => {
    setSelectedStyle(style.name);
    setShowStyleModal(false);
  };

  // التمرير إلى أسفل عند إضافة رسالة جديدة
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // رندر رسالة فردية
  const renderMessage = ({ item }: { item: Message }) => (
    <ChatMessage message={item} />
  );

  // رندر نمط الكتابة
  const renderStyleItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.styleItem}
      onPress={() => handleStyleChange(item)}
    >
      <Icon name={item.icon} size={24} color="#2196F3" />
      <Text style={styles.styleItemText}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* قائمة الرسائل */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        onContentSizeChange={() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }}
      />

      {/* شريط الأدوات للكتاب */}
      <View style={[
        styles.toolbar,
        { flexDirection: isRTL ? 'row-reverse' : 'row' }
      ]}>
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={createNewSession}
        >
          <Icon name="add" size={20} color="#2196F3" />
          <Text style={styles.toolbarButtonText}>
            {t('chat.newChat')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setShowStyleModal(true)}
        >
          <Icon name="style" size={20} color="#9C27B0" />
          <Text style={[styles.toolbarButtonText, { color: '#9C27B0' }]}>
            {selectedStyle}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={handleClearChat}
        >
          <Icon name="clear" size={20} color="#f44336" />
          <Text style={[styles.toolbarButtonText, { color: '#f44336' }]}>
            {t('chat.clearChat')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* حقل إدخال الرسائل */}
      <ChatInput
        placeholder={t('chat.writer.placeholder')}
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
        multiline={true}
      />

      {/* مودال اختيار نمط الكتابة */}
      <Modal
        visible={showStyleModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowStyleModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>اختر نمط الكتابة</Text>
              <TouchableOpacity
                onPress={() => setShowStyleModal(false)}
                style={styles.closeButton}
              >
                <Icon name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={writingStyles}
              renderItem={renderStyleItem}
              keyExtractor={(item) => item.id}
              numColumns={2}
              contentContainerStyle={styles.stylesGrid}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 16,
  },
  toolbar: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#f9f9f9',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    justifyContent: 'space-around',
  },
  toolbarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  toolbarButtonText: {
    fontSize: 12,
    color: '#2196F3',
    marginLeft: 4,
    fontFamily: 'Arial',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    fontFamily: 'Arial',
  },
  closeButton: {
    padding: 4,
  },
  stylesGrid: {
    paddingVertical: 10,
  },
  styleItem: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    margin: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  styleItemText: {
    fontSize: 14,
    color: '#333',
    marginTop: 8,
    fontFamily: 'Arial',
  },
});

export default WriterChatScreen;
