{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@components/*": ["components/*"], "@screens/*": ["screens/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@types/*": ["types/*"]}, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["src/**/*", "index.js"], "exclude": ["node_modules", "android", "ios"]}