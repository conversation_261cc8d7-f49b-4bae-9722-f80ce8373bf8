{"extends": "@react-native/typescript-config", "compilerOptions": {"baseUrl": "./src", "paths": {"@/*": ["*"], "@components/*": ["components/*"], "@screens/*": ["screens/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@types/*": ["types/*"]}, "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "skipLibCheck": true}, "include": ["src/**/*", "index.js"], "exclude": ["node_modules", "android", "ios"]}