import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import * as Animatable from 'react-native-animatable';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Clipboard from '@react-native-clipboard/clipboard';
import { useTranslation } from 'react-i18next';
import { Message } from '../types';
import i18nService from '../services/i18nService';

interface ChatMessageProps {
  message: Message;
  onCopy?: () => void;
  onShare?: () => void;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message, onCopy, onShare }) => {
  const { t } = useTranslation();
  const isRTL = i18nService.isRTL();

  const handleCopy = () => {
    Clipboard.setString(message.text);
    Alert.alert(t('common.success'), t('chat.copyMessage'));
    onCopy?.();
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderMessageContent = () => {
    // إذا كانت الرسالة تحتوي على كود
    if (message.type === 'code' || message.text.includes('```')) {
      const parts = message.text.split('```');
      return parts.map((part, index) => {
        if (index % 2 === 1) {
          // هذا جزء كود
          return (
            <View key={index} style={styles.codeBlock}>
              <Text style={styles.codeText}>{part}</Text>
            </View>
          );
        } else {
          // هذا نص عادي
          return part ? (
            <Text key={index} style={[
              styles.messageText,
              { textAlign: isRTL ? 'right' : 'left' }
            ]}>
              {part}
            </Text>
          ) : null;
        }
      });
    }

    return (
      <Text style={[
        styles.messageText,
        { textAlign: isRTL ? 'right' : 'left' }
      ]}>
        {message.text}
      </Text>
    );
  };

  return (
    <Animatable.View
      animation="fadeInUp"
      duration={300}
      style={[
        styles.container,
        {
          alignSelf: message.isUser ? (isRTL ? 'flex-start' : 'flex-end') : (isRTL ? 'flex-end' : 'flex-start'),
          marginLeft: message.isUser ? (isRTL ? 0 : 50) : 0,
          marginRight: message.isUser ? (isRTL ? 50 : 0) : 0,
        }
      ]}
    >
      <View style={[
        styles.messageBubble,
        message.isUser ? styles.userMessage : styles.aiMessage,
        { borderTopLeftRadius: message.isUser && !isRTL ? 4 : 16 },
        { borderTopRightRadius: message.isUser && isRTL ? 4 : 16 },
      ]}>
        {/* أيقونة المرسل */}
        {!message.isUser && (
          <View style={styles.aiIcon}>
            <Icon name="psychology" size={16} color="#2196F3" />
          </View>
        )}

        {/* محتوى الرسالة */}
        <View style={styles.messageContent}>
          {renderMessageContent()}
        </View>

        {/* وقت الإرسال */}
        <Text style={[
          styles.timestamp,
          { textAlign: isRTL ? 'right' : 'left' }
        ]}>
          {formatTime(message.timestamp)}
        </Text>

        {/* أزرار الإجراءات */}
        <View style={[
          styles.actionsContainer,
          { flexDirection: isRTL ? 'row-reverse' : 'row' }
        ]}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleCopy}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Icon name="content-copy" size={16} color="#666" />
          </TouchableOpacity>

          {onShare && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={onShare}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Icon name="share" size={16} color="#666" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    marginHorizontal: 16,
  },
  messageBubble: {
    maxWidth: '85%',
    padding: 12,
    borderRadius: 16,
    position: 'relative',
  },
  userMessage: {
    backgroundColor: '#2196F3',
  },
  aiMessage: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  aiIcon: {
    position: 'absolute',
    top: -8,
    left: 8,
    backgroundColor: '#fff',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  messageContent: {
    marginTop: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
    color: '#333',
    fontFamily: 'Arial',
  },
  codeBlock: {
    backgroundColor: '#2d3748',
    padding: 12,
    borderRadius: 8,
    marginVertical: 4,
  },
  codeText: {
    fontSize: 14,
    color: '#e2e8f0',
    fontFamily: 'Courier New',
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    fontFamily: 'Arial',
  },
  actionsContainer: {
    position: 'absolute',
    top: -12,
    right: 8,
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButton: {
    padding: 4,
    marginHorizontal: 2,
  },
});

export default ChatMessage;
