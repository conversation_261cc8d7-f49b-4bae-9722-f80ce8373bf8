import { GoogleGenerativeAI } from '@google/generative-ai';

// مفتاح API - يجب وضعه في متغيرات البيئة في الإنتاج
const API_KEY = 'AIzaSyBFMEbVLLqXztDjHNBD87Ul6Q6qTz0nThU'; // يجب استبداله بمفتاح حقيقي

class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    this.genAI = new GoogleGenerativeAI(API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
  }

  // دردشة عامة
  async generateGeneralResponse(message: string, context?: string): Promise<string> {
    try {
      const prompt = this.buildGeneralPrompt(message, context);
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating general response:', error);
      throw new Error('فشل في الحصول على رد من الذكاء الاصطناعي');
    }
  }

  // مساعد المطورين
  async generateDeveloperResponse(message: string, codeContext?: string): Promise<string> {
    try {
      const prompt = this.buildDeveloperPrompt(message, codeContext);
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating developer response:', error);
      throw new Error('فشل في الحصول على رد من مساعد المطورين');
    }
  }

  // مساعد الكتاب
  async generateWriterResponse(message: string, writingStyle?: string): Promise<string> {
    try {
      const prompt = this.buildWriterPrompt(message, writingStyle);
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating writer response:', error);
      throw new Error('فشل في الحصول على رد من مساعد الكتاب');
    }
  }

  // بناء prompt للدردشة العامة
  private buildGeneralPrompt(message: string, context?: string): string {
    let prompt = `أنت مساعد ذكي يتحدث العربية ويساعد المستخدمين في مختلف المواضيع. 
    كن مفيداً ومهذباً ودقيقاً في إجاباتك.
    
    ${context ? `السياق: ${context}\n\n` : ''}
    
    السؤال: ${message}
    
    الرد:`;
    
    return prompt;
  }

  // بناء prompt لمساعد المطورين
  private buildDeveloperPrompt(message: string, codeContext?: string): string {
    let prompt = `أنت مساعد ذكي متخصص في البرمجة وتطوير البرمجيات. 
    تساعد المطورين في:
    - حل مشاكل البرمجة
    - شرح الأكواد
    - تحسين الأداء
    - أفضل الممارسات
    - مراجعة الكود
    
    ${codeContext ? `الكود المرفق:\n\`\`\`\n${codeContext}\n\`\`\`\n\n` : ''}
    
    السؤال: ${message}
    
    الرد (استخدم تنسيق markdown للكود):`;
    
    return prompt;
  }

  // بناء prompt لمساعد الكتاب
  private buildWriterPrompt(message: string, writingStyle?: string): string {
    let prompt = `أنت مساعد ذكي متخصص في الكتابة والأدب. 
    تساعد الكتاب في:
    - توليد الأفكار
    - كتابة القصص والمقالات
    - تحسين الأسلوب
    - التدقيق اللغوي
    - التلخيص
    
    ${writingStyle ? `نمط الكتابة المطلوب: ${writingStyle}\n\n` : ''}
    
    الطلب: ${message}
    
    الرد:`;
    
    return prompt;
  }

  // تحديث مفتاح API
  updateApiKey(apiKey: string) {
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
  }

  // فحص صحة مفتاح API
  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const tempGenAI = new GoogleGenerativeAI(apiKey);
      const tempModel = tempGenAI.getGenerativeModel({ model: 'gemini-pro' });
      await tempModel.generateContent('Test');
      return true;
    } catch (error) {
      return false;
    }
  }
}

export default new GeminiService();
