import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTranslation } from 'react-i18next';
import { useApp } from '../context/AppContext';
import { AppSettings } from '../types';
import i18nService from '../services/i18nService';
import geminiService from '../services/geminiService';
import searchService from '../services/searchService';

const SettingsScreen: React.FC = () => {
  const { t } = useTranslation();
  const { state, saveSettings, dispatch } = useApp();
  const [tempSettings, setTempSettings] = useState<AppSettings>(state.settings);
  const [showApiModal, setShowApiModal] = useState(false);
  const [apiKeyInput, setApiKeyInput] = useState('');
  const [apiKeyType, setApiKeyType] = useState<'gemini' | 'search'>('gemini');
  const [isTestingKey, setIsTestingKey] = useState(false);
  const isRTL = i18nService.isRTL();

  // تغيير اللغة
  const handleLanguageChange = async (language: 'ar' | 'en') => {
    const newSettings = { ...tempSettings, language };
    setTempSettings(newSettings);
    await saveSettings(newSettings);
    await i18nService.changeLanguage(language);
    
    Alert.alert(
      t('common.success'),
      'تم تغيير اللغة بنجاح. قد تحتاج لإعادة تشغيل التطبيق لرؤية التغييرات كاملة.'
    );
  };

  // تغيير المظهر
  const handleThemeChange = async (theme: 'light' | 'dark') => {
    const newSettings = { ...tempSettings, theme };
    setTempSettings(newSettings);
    await saveSettings(newSettings);
  };

  // فتح مودال API
  const openApiModal = (type: 'gemini' | 'search') => {
    setApiKeyType(type);
    setApiKeyInput(type === 'gemini' ? tempSettings.geminiApiKey || '' : tempSettings.searchApiKey || '');
    setShowApiModal(true);
  };

  // حفظ مفتاح API
  const handleSaveApiKey = async () => {
    if (!apiKeyInput.trim()) {
      Alert.alert(t('common.error'), 'يرجى إدخال مفتاح API');
      return;
    }

    setIsTestingKey(true);

    try {
      let isValid = false;

      if (apiKeyType === 'gemini') {
        isValid = await geminiService.validateApiKey(apiKeyInput);
        if (isValid) {
          geminiService.updateApiKey(apiKeyInput);
        }
      } else {
        isValid = await searchService.validateApiKey(apiKeyInput);
        if (isValid) {
          searchService.updateApiKey(apiKeyInput);
        }
      }

      if (isValid) {
        const newSettings = {
          ...tempSettings,
          [apiKeyType === 'gemini' ? 'geminiApiKey' : 'searchApiKey']: apiKeyInput,
        };
        setTempSettings(newSettings);
        await saveSettings(newSettings);
        setShowApiModal(false);
        Alert.alert(t('common.success'), t('settings.api.keyValid'));
      } else {
        Alert.alert(t('common.error'), t('settings.api.keyInvalid'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('settings.api.keyInvalid'));
    } finally {
      setIsTestingKey(false);
    }
  };

  // مسح سجل المحادثات
  const handleClearHistory = () => {
    Alert.alert(
      t('settings.data.clearHistory'),
      t('settings.data.clearHistoryConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => {
            dispatch({ type: 'CLEAR_ALL_SESSIONS' });
            Alert.alert(t('common.success'), 'تم مسح جميع المحادثات');
          },
        },
      ]
    );
  };

  // رندر عنصر إعداد
  const renderSettingItem = (
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightComponent?: React.ReactNode,
    icon?: string
  ) => (
    <TouchableOpacity
      style={[
        styles.settingItem,
        { flexDirection: isRTL ? 'row-reverse' : 'row' }
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      {icon && (
        <View style={[
          styles.settingIcon,
          { marginRight: isRTL ? 0 : 12, marginLeft: isRTL ? 12 : 0 }
        ]}>
          <Icon name={icon} size={24} color="#2196F3" />
        </View>
      )}
      
      <View style={styles.settingContent}>
        <Text style={[
          styles.settingTitle,
          { textAlign: isRTL ? 'right' : 'left' }
        ]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={[
            styles.settingSubtitle,
            { textAlign: isRTL ? 'right' : 'left' }
          ]}>
            {subtitle}
          </Text>
        )}
      </View>

      {rightComponent && (
        <View style={styles.settingRight}>
          {rightComponent}
        </View>
      )}

      {onPress && (
        <Icon 
          name={isRTL ? "chevron-left" : "chevron-right"} 
          size={24} 
          color="#ccc" 
        />
      )}
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* قسم اللغة */}
      <View style={styles.section}>
        <Text style={[
          styles.sectionTitle,
          { textAlign: isRTL ? 'right' : 'left' }
        ]}>
          {t('settings.language.title')}
        </Text>

        {renderSettingItem(
          t('settings.language.arabic'),
          undefined,
          () => handleLanguageChange('ar'),
          <Switch
            value={tempSettings.language === 'ar'}
            onValueChange={() => handleLanguageChange('ar')}
            trackColor={{ false: '#ccc', true: '#2196F3' }}
          />,
          'language'
        )}

        {renderSettingItem(
          t('settings.language.english'),
          undefined,
          () => handleLanguageChange('en'),
          <Switch
            value={tempSettings.language === 'en'}
            onValueChange={() => handleLanguageChange('en')}
            trackColor={{ false: '#ccc', true: '#2196F3' }}
          />,
          'language'
        )}
      </View>

      {/* قسم المظهر */}
      <View style={styles.section}>
        <Text style={[
          styles.sectionTitle,
          { textAlign: isRTL ? 'right' : 'left' }
        ]}>
          {t('settings.theme.title')}
        </Text>

        {renderSettingItem(
          t('settings.theme.light'),
          undefined,
          () => handleThemeChange('light'),
          <Switch
            value={tempSettings.theme === 'light'}
            onValueChange={() => handleThemeChange('light')}
            trackColor={{ false: '#ccc', true: '#2196F3' }}
          />,
          'light-mode'
        )}

        {renderSettingItem(
          t('settings.theme.dark'),
          undefined,
          () => handleThemeChange('dark'),
          <Switch
            value={tempSettings.theme === 'dark'}
            onValueChange={() => handleThemeChange('dark')}
            trackColor={{ false: '#ccc', true: '#2196F3' }}
          />,
          'dark-mode'
        )}
      </View>

      {/* قسم API */}
      <View style={styles.section}>
        <Text style={[
          styles.sectionTitle,
          { textAlign: isRTL ? 'right' : 'left' }
        ]}>
          {t('settings.api.title')}
        </Text>

        {renderSettingItem(
          t('settings.api.geminiKey'),
          tempSettings.geminiApiKey ? 'تم تكوينه' : 'غير مكون',
          () => openApiModal('gemini'),
          undefined,
          'key'
        )}

        {renderSettingItem(
          t('settings.api.searchKey'),
          tempSettings.searchApiKey ? 'تم تكوينه' : 'غير مكون',
          () => openApiModal('search'),
          undefined,
          'search'
        )}
      </View>

      {/* قسم إدارة البيانات */}
      <View style={styles.section}>
        <Text style={[
          styles.sectionTitle,
          { textAlign: isRTL ? 'right' : 'left' }
        ]}>
          {t('settings.data.title')}
        </Text>

        {renderSettingItem(
          t('settings.data.clearHistory'),
          'مسح جميع المحادثات المحفوظة',
          handleClearHistory,
          undefined,
          'delete'
        )}
      </View>

      {/* قسم حول التطبيق */}
      <View style={styles.section}>
        <Text style={[
          styles.sectionTitle,
          { textAlign: isRTL ? 'right' : 'left' }
        ]}>
          {t('settings.about.title')}
        </Text>

        {renderSettingItem(
          t('settings.about.version'),
          '1.0.0',
          undefined,
          undefined,
          'info'
        )}

        {renderSettingItem(
          t('settings.about.developer'),
          'Red AI Team',
          undefined,
          undefined,
          'person'
        )}
      </View>

      {/* مودال API */}
      <Modal
        visible={showApiModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowApiModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {apiKeyType === 'gemini' ? t('settings.api.geminiKey') : t('settings.api.searchKey')}
              </Text>
              <TouchableOpacity
                onPress={() => setShowApiModal(false)}
                style={styles.closeButton}
              >
                <Icon name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <TextInput
              style={[
                styles.apiKeyInput,
                { textAlign: isRTL ? 'right' : 'left' }
              ]}
              value={apiKeyInput}
              onChangeText={setApiKeyInput}
              placeholder={t('settings.api.keyPlaceholder')}
              placeholderTextColor="#999"
              secureTextEntry={true}
              multiline={false}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowApiModal(false)}
              >
                <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleSaveApiKey}
                disabled={isTestingKey}
              >
                <Text style={styles.saveButtonText}>
                  {isTestingKey ? 'جاري الاختبار...' : t('common.save')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  section: {
    backgroundColor: '#fff',
    marginVertical: 8,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    paddingHorizontal: 16,
    marginBottom: 16,
    fontFamily: 'Arial',
  },
  settingItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f8ff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    color: '#333',
    fontFamily: 'Arial',
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
    fontFamily: 'Arial',
  },
  settingRight: {
    marginHorizontal: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    width: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    fontFamily: 'Arial',
  },
  closeButton: {
    padding: 4,
  },
  apiKeyInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 20,
    fontFamily: 'Arial',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
  },
  saveButton: {
    backgroundColor: '#2196F3',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
    fontFamily: 'Arial',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
    fontFamily: 'Arial',
  },
});

export default SettingsScreen;
