import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { I18nManager } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// استيراد ملفات الترجمة
import ar from '../locales/ar.json';
import en from '../locales/en.json';

const LANGUAGE_STORAGE_KEY = 'app_language';

// إعداد i18n
i18n
  .use(initReactI18next)
  .init({
    resources: {
      ar: { translation: ar },
      en: { translation: en },
    },
    lng: 'ar', // اللغة الافتراضية
    fallbackLng: 'ar',
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  });

class I18nService {
  // تغيير اللغة
  async changeLanguage(language: 'ar' | 'en') {
    try {
      // حفظ اللغة في التخزين المحلي
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);
      
      // تغيير اللغة في i18n
      await i18n.changeLanguage(language);
      
      // تحديد اتجاه النص (RTL للعربية، LTR للإنجليزية)
      const isRTL = language === 'ar';
      I18nManager.allowRTL(isRTL);
      I18nManager.forceRTL(isRTL);
      
      return true;
    } catch (error) {
      console.error('Error changing language:', error);
      return false;
    }
  }

  // تحميل اللغة المحفوظة
  async loadSavedLanguage() {
    try {
      const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
      if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
        await this.changeLanguage(savedLanguage);
        return savedLanguage;
      }
      return 'ar'; // اللغة الافتراضية
    } catch (error) {
      console.error('Error loading saved language:', error);
      return 'ar';
    }
  }

  // الحصول على اللغة الحالية
  getCurrentLanguage(): 'ar' | 'en' {
    return i18n.language as 'ar' | 'en';
  }

  // فحص إذا كانت اللغة الحالية عربية
  isRTL(): boolean {
    return this.getCurrentLanguage() === 'ar';
  }

  // الحصول على ترجمة
  translate(key: string, options?: any): string {
    return i18n.t(key, options) as string;
  }

  // الحصول على اتجاه النص
  getTextDirection(): 'rtl' | 'ltr' {
    return this.isRTL() ? 'rtl' : 'ltr';
  }

  // الحصول على محاذاة النص
  getTextAlign(): 'right' | 'left' {
    return this.isRTL() ? 'right' : 'left';
  }

  // الحصول على اتجاه flex
  getFlexDirection(): 'row' | 'row-reverse' {
    return this.isRTL() ? 'row-reverse' : 'row';
  }
}

export default new I18nService();
export { i18n };
