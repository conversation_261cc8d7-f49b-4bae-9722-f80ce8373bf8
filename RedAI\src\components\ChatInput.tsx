import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Keyboard,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTranslation } from 'react-i18next';
import i18nService from '../services/i18nService';

interface ChatInputProps {
  placeholder?: string;
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  multiline?: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({
  placeholder,
  onSendMessage,
  isLoading = false,
  multiline = true,
}) => {
  const { t } = useTranslation();
  const [message, setMessage] = useState('');
  const isRTL = i18nService.isRTL();

  const handleSend = () => {
    const trimmedMessage = message.trim();
    
    if (!trimmedMessage) {
      Alert.alert(t('common.error'), 'يرجى كتابة رسالة قبل الإرسال');
      return;
    }

    if (trimmedMessage.length > 4000) {
      Alert.alert(t('common.error'), 'الرسالة طويلة جداً. يرجى تقصيرها.');
      return;
    }

    onSendMessage(trimmedMessage);
    setMessage('');
    Keyboard.dismiss();
  };

  const handleKeyPress = (event: any) => {
    if (event.nativeEvent.key === 'Enter' && !event.nativeEvent.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  return (
    <View style={[
      styles.container,
      { flexDirection: isRTL ? 'row-reverse' : 'row' }
    ]}>
      {/* حقل إدخال النص */}
      <View style={styles.inputContainer}>
        <TextInput
          style={[
            styles.textInput,
            {
              textAlign: isRTL ? 'right' : 'left',
              writingDirection: isRTL ? 'rtl' : 'ltr',
            }
          ]}
          value={message}
          onChangeText={setMessage}
          placeholder={placeholder || t('chat.general.placeholder')}
          placeholderTextColor="#999"
          multiline={multiline}
          maxLength={4000}
          editable={!isLoading}
          onKeyPress={handleKeyPress}
          returnKeyType="send"
        />

        {/* عداد الأحرف */}
        {message.length > 3500 && (
          <View style={styles.characterCount}>
            <Icon 
              name="warning" 
              size={12} 
              color={message.length > 3800 ? '#f44336' : '#ff9800'} 
            />
            <Text style={[
              styles.characterCountText,
              { color: message.length > 3800 ? '#f44336' : '#ff9800' }
            ]}>
              {message.length}/4000
            </Text>
          </View>
        )}
      </View>

      {/* زر الإرسال */}
      <TouchableOpacity
        style={[
          styles.sendButton,
          {
            backgroundColor: message.trim() && !isLoading ? '#2196F3' : '#ccc',
            marginLeft: isRTL ? 0 : 8,
            marginRight: isRTL ? 8 : 0,
          }
        ]}
        onPress={handleSend}
        disabled={!message.trim() || isLoading}
        activeOpacity={0.7}
      >
        {isLoading ? (
          <Icon name="hourglass-empty" size={24} color="#fff" />
        ) : (
          <Icon 
            name={isRTL ? "arrow-back" : "send"} 
            size={24} 
            color="#fff" 
          />
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    alignItems: 'flex-end',
  },
  inputContainer: {
    flex: 1,
    position: 'relative',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    maxHeight: 120,
    backgroundColor: '#f9f9f9',
    fontFamily: 'Arial',
  },
  characterCount: {
    position: 'absolute',
    bottom: 4,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  characterCountText: {
    fontSize: 10,
    marginLeft: 2,
    fontFamily: 'Arial',
  },
  sendButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});

export default ChatInput;
