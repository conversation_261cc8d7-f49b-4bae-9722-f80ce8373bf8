// Types للتطبيق
export interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  type?: 'text' | 'code' | 'search';
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  type: 'general' | 'developer' | 'writer';
  createdAt: Date;
  updatedAt: Date;
}

export interface AppSettings {
  language: 'ar' | 'en';
  theme: 'light' | 'dark';
  geminiApiKey?: string;
  searchApiKey?: string;
}

export interface SearchResult {
  title: string;
  snippet: string;
  url: string;
}

export type RootStackParamList = {
  Splash: undefined;
  Welcome: undefined;
  Main: undefined;
  Chat: { sessionId?: string; type?: 'general' | 'developer' | 'writer' };
  Settings: undefined;
};

export type MainTabParamList = {
  GeneralChat: undefined;
  DeveloperChat: undefined;
  WriterChat: undefined;
  Settings: undefined;
};
