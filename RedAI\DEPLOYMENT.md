# دليل النشر - Red AI

## إعداد التطبيق للنشر

### 1. تحديث معلومات التطبيق

#### Android (android/app/build.gradle):
```gradle
android {
    defaultConfig {
        applicationId "com.redai.app"
        versionCode 1
        versionName "1.0.0"
        // ...
    }
}
```

#### iOS (ios/RedAI/Info.plist):
```xml
<key>CFBundleDisplayName</key>
<string>Red AI</string>
<key>CFBundleVersion</key>
<string>1.0.0</string>
```

### 2. إعداد الأيقونات

#### Android:
- ضع الأيقونات في: `android/app/src/main/res/`
- الأحجام المطلوبة:
  - mipmap-hdpi: 72x72
  - mipmap-mdpi: 48x48
  - mipmap-xhdpi: 96x96
  - mipmap-xxhdpi: 144x144
  - mipmap-xxxhdpi: 192x192

#### iOS:
- استخدم Xcode لإضافة الأيقونات
- الأحجام المطلوبة: 20x20 إلى 1024x1024

### 3. إعداد Splash Screen

#### Android:
```xml
<!-- android/app/src/main/res/drawable/launch_screen.xml -->
<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@color/primary_color" />
    <item>
        <bitmap
            android:gravity="center"
            android:src="@drawable/logo" />
    </item>
</layer-list>
```

### 4. تحسين الأداء

#### تفعيل ProGuard (Android):
```gradle
android {
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

#### تحسين Bundle Size:
```bash
# تحليل حجم Bundle
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res

# فحص الحجم
du -sh android/app/src/main/assets/index.android.bundle
```

## بناء التطبيق للنشر

### Android APK:
```bash
cd android
./gradlew assembleRelease
```

### Android AAB (للـ Play Store):
```bash
cd android
./gradlew bundleRelease
```

### iOS (في Xcode):
1. Product → Archive
2. Distribute App
3. اختر App Store Connect

## إعداد مفاتيح التوقيع

### Android:
```bash
# إنشاء keystore
keytool -genkey -v -keystore red-ai-release-key.keystore -alias red-ai-key-alias -keyalg RSA -keysize 2048 -validity 10000

# إضافة في android/gradle.properties
MYAPP_RELEASE_STORE_FILE=red-ai-release-key.keystore
MYAPP_RELEASE_KEY_ALIAS=red-ai-key-alias
MYAPP_RELEASE_STORE_PASSWORD=****
MYAPP_RELEASE_KEY_PASSWORD=****
```

### iOS:
- استخدم Apple Developer Account
- أنشئ Distribution Certificate
- أنشئ Provisioning Profile

## اختبار النسخة النهائية

### اختبارات مطلوبة:
1. **الوظائف الأساسية:**
   - تشغيل التطبيق بدون أخطاء
   - جميع الشاشات تعمل
   - التنقل سليم

2. **اختبار API:**
   - Gemini API يعمل
   - Search API يعمل (اختياري)
   - معالجة الأخطاء

3. **اختبار اللغات:**
   - العربية تظهر صحيح
   - RTL يعمل
   - تبديل اللغة

4. **اختبار الأداء:**
   - سرعة التشغيل
   - استهلاك الذاكرة
   - استهلاك البطارية

### أجهزة الاختبار:
- هواتف بأحجام مختلفة
- إصدارات Android/iOS مختلفة
- أجهزة قديمة ومتوسطة الأداء

## نشر التطبيق

### Google Play Store:

#### 1. إعداد Play Console:
- أنشئ حساب مطور
- ادفع رسوم التسجيل ($25)
- أنشئ تطبيق جديد

#### 2. معلومات التطبيق:
```
اسم التطبيق: Red AI - مساعد ذكي شامل
الوصف القصير: مساعد ذكي متطور للدردشة والبرمجة والكتابة
الوصف الطويل: [راجع ملف STORE_DESCRIPTION.md]
الفئة: Productivity
التقييم: Everyone
```

#### 3. الصور المطلوبة:
- أيقونة التطبيق: 512x512
- صور الشاشات: 1080x1920 (على الأقل 2)
- صورة مميزة: 1024x500

### Apple App Store:

#### 1. إعداد App Store Connect:
- حساب Apple Developer ($99/سنة)
- أنشئ App ID
- أنشئ تطبيق في App Store Connect

#### 2. معلومات التطبيق:
- نفس المعلومات المذكورة أعلاه
- إضافة Privacy Policy
- إضافة Terms of Service

## ما بعد النشر

### 1. مراقبة الأداء:
- Firebase Analytics
- Crashlytics للأخطاء
- مراجعات المستخدمين

### 2. التحديثات:
- إصلاح الأخطاء
- إضافة مزايا جديدة
- تحسين الأداء

### 3. التسويق:
- وسائل التواصل الاجتماعي
- المواقع التقنية
- المجتمعات المطورين

## نصائح مهمة

### الأمان:
- لا تضع مفاتيح API في الكود
- استخدم متغيرات البيئة
- اشفر البيانات الحساسة

### الامتثال:
- Privacy Policy مطلوبة
- GDPR للمستخدمين الأوروبيين
- حقوق الطفل (COPPA)

### الدعم:
- أنشئ قناة دعم
- وثق الأخطاء الشائعة
- رد على مراجعات المستخدمين

## قائمة فحص النشر

- [ ] اختبار شامل للتطبيق
- [ ] تحديث رقم الإصدار
- [ ] إضافة الأيقونات والصور
- [ ] إعداد مفاتيح التوقيع
- [ ] بناء النسخة النهائية
- [ ] اختبار النسخة النهائية
- [ ] إعداد صفحة المتجر
- [ ] رفع التطبيق
- [ ] انتظار الموافقة
- [ ] نشر التطبيق

---

**مبروك! 🎉 تطبيق Red AI جاهز للنشر**

تذكر أن النشر عملية تتطلب صبر ودقة. اتبع الإرشادات بعناية وستحصل على تطبيق ناجح في المتاجر.
