import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  Alert,
  Text,
  TouchableOpacity,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';
import uuid from 'react-native-uuid';

// استيراد المكونات والخدمات
import ChatMessage from '../components/ChatMessage';
import ChatInput from '../components/ChatInput';
import { useApp } from '../context/AppContext';
import { Message, ChatSession } from '../types';
import geminiService from '../services/geminiService';
import i18nService from '../services/i18nService';

const DeveloperChatScreen: React.FC = () => {
  const { t } = useTranslation();
  const { state, dispatch } = useApp();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  const flatListRef = useRef<FlatList>(null);
  const isRTL = i18nService.isRTL();

  // إنشاء جلسة جديدة أو تحميل الجلسة الحالية
  useEffect(() => {
    const existingSession = state.chatSessions.find(
      session => session.type === 'developer'
    );

    if (existingSession) {
      setCurrentSessionId(existingSession.id);
      setMessages(existingSession.messages);
    } else {
      createNewSession();
    }
  }, []);

  // إنشاء جلسة دردشة جديدة
  const createNewSession = () => {
    const sessionId = uuid.v4() as string;
    const newSession: ChatSession = {
      id: sessionId,
      title: t('chat.developer.title'),
      messages: [
        {
          id: uuid.v4() as string,
          text: t('chat.developer.emptyState'),
          isUser: false,
          timestamp: new Date(),
          type: 'text',
        }
      ],
      type: 'developer',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    dispatch({ type: 'CREATE_SESSION', payload: newSession });
    setCurrentSessionId(sessionId);
    setMessages(newSession.messages);
  };

  // إرسال رسالة
  const handleSendMessage = async (messageText: string) => {
    if (!messageText.trim()) return;

    const userMessage: Message = {
      id: uuid.v4() as string,
      text: messageText,
      isUser: true,
      timestamp: new Date(),
      type: 'text',
    };

    // إضافة رسالة المستخدم
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    
    // إضافة الرسالة إلى الجلسة
    dispatch({
      type: 'ADD_MESSAGE',
      payload: { sessionId: currentSessionId, message: userMessage },
    });

    setIsLoading(true);

    try {
      // استخراج الكود من الرسالة إن وجد
      const codeMatch = messageText.match(/```[\s\S]*?```/g);
      const codeContext = codeMatch ? codeMatch.join('\n') : undefined;

      // الحصول على رد من Gemini للمطورين
      const aiResponse = await geminiService.generateDeveloperResponse(
        messageText,
        codeContext
      );

      const aiMessage: Message = {
        id: uuid.v4() as string,
        text: aiResponse,
        isUser: false,
        timestamp: new Date(),
        type: aiResponse.includes('```') ? 'code' : 'text',
      };

      // إضافة رد الذكاء الاصطناعي
      const finalMessages = [...updatedMessages, aiMessage];
      setMessages(finalMessages);

      // إضافة الرسالة إلى الجلسة
      dispatch({
        type: 'ADD_MESSAGE',
        payload: { sessionId: currentSessionId, message: aiMessage },
      });

    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert(
        t('common.error'),
        error instanceof Error ? error.message : t('errors.generalError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  // مسح المحادثة
  const handleClearChat = () => {
    Alert.alert(
      t('chat.clearChat'),
      'هل أنت متأكد من مسح هذه المحادثة؟',
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => {
            dispatch({ type: 'DELETE_SESSION', payload: currentSessionId });
            createNewSession();
          },
        },
      ]
    );
  };

  // إدراج قالب كود
  const insertCodeTemplate = () => {
    const template = '```javascript\n// اكتب الكود هنا\n\n```';
    // يمكن تحسين هذا لإدراج القالب في حقل الإدخال
    Alert.alert('قالب الكود', 'استخدم هذا التنسيق لإدراج الكود:\n\n' + template);
  };

  // التمرير إلى أسفل عند إضافة رسالة جديدة
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // رندر رسالة فردية
  const renderMessage = ({ item }: { item: Message }) => (
    <ChatMessage message={item} />
  );

  return (
    <View style={styles.container}>
      {/* قائمة الرسائل */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        onContentSizeChange={() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }}
      />

      {/* شريط الأدوات للمطورين */}
      <View style={[
        styles.toolbar,
        { flexDirection: isRTL ? 'row-reverse' : 'row' }
      ]}>
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={createNewSession}
        >
          <Icon name="add" size={20} color="#2196F3" />
          <Text style={styles.toolbarButtonText}>
            {t('chat.newChat')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={insertCodeTemplate}
        >
          <Icon name="code" size={20} color="#4CAF50" />
          <Text style={[styles.toolbarButtonText, { color: '#4CAF50' }]}>
            قالب كود
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={handleClearChat}
        >
          <Icon name="clear" size={20} color="#f44336" />
          <Text style={[styles.toolbarButtonText, { color: '#f44336' }]}>
            {t('chat.clearChat')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* حقل إدخال الرسائل */}
      <ChatInput
        placeholder={t('chat.developer.placeholder')}
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
        multiline={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 16,
  },
  toolbar: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#f9f9f9',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    justifyContent: 'space-around',
  },
  toolbarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  toolbarButtonText: {
    fontSize: 12,
    color: '#2196F3',
    marginLeft: 4,
    fontFamily: 'Arial',
  },
});

export default DeveloperChatScreen;
