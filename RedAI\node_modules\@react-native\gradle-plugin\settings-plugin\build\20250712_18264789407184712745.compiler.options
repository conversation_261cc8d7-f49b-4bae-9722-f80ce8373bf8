"-Xallow-no-source-files" "-classpath" "D:\\.gradle\\caches\\8.14.3\\generated-gradle-jars\\gradle-api-8.14.3.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-ant-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-astbuilder-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-console-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-datetime-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-dateutil-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-groovydoc-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-json-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-nio-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-sql-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-templates-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-test-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\groovy-xml-3.0.24.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\javaparser-core-3.17.0.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\kotlin-stdlib-2.0.21.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\kotlin-reflect-2.0.21.jar;D:\\.gradle\\wrapper\\dists\\gradle-8.14.3-bin\\cv11ve7ro1n3o1j4so8xd9n66\\gradle-8.14.3\\lib\\gradle-installation-beacon-8.14.3.jar;D:\\my project\\app\\RED AI\\app\\RedAI\\node_modules\\@react-native\\gradle-plugin\\shared\\build\\libs\\shared.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\2.1.20\\aa8ca79cd50578314f6d1180c47cbe14c0fee567\\kotlin-stdlib-2.1.20.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\com.google.code.gson\\gson\\2.8.9\\8a432c1d6825781e21a02db2e2c33c5fde2833b9\\gson-2.8.9.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\guava\\31.0.1-jre\\119ea2b2bc205b138974d351777b20f02b92704b\\guava-31.0.1-jre.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup\\javapoet\\1.13.0\\d6562d385049f35eb50403fa86bb11cce76b866a\\javapoet-1.13.0.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\13.0\\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\\annotations-13.0.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\failureaccess\\1.0.1\\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\\failureaccess-1.0.1.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\listenablefuture\\9999.0-empty-to-avoid-conflict-with-guava\\b421526c5f297295adef1c886e5246c39d4ac629\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\com.google.code.findbugs\\jsr305\\3.0.2\\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\\jsr305-3.0.2.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\org.checkerframework\\checker-qual\\3.12.0\\d5692f0526415fcc6de94bb5bfbd3afd9dd3b3e5\\checker-qual-3.12.0.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\com.google.errorprone\\error_prone_annotations\\2.7.1\\458d9042f7aa6fa9a634df902b37f544e15aacac\\error_prone_annotations-2.7.1.jar;D:\\.gradle\\caches\\modules-2\\files-2.1\\com.google.j2objc\\j2objc-annotations\\1.3\\ba035118bc8bac37d7eff77700720999acd9986d\\j2objc-annotations-1.3.jar" "-d" "D:\\my project\\app\\RED AI\\app\\RedAI\\node_modules\\@react-native\\gradle-plugin\\settings-plugin\\build\\classes\\kotlin\\main" "-jdk-home" "C:\\Program Files\\Java\\jdk-17" "-jvm-target" "11" "-module-name" "settings-plugin" "-no-reflect" "-no-stdlib" "-api-version" "1.8" "-Xplugin=D:\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-compiler-embeddable\\2.1.20\\efd094224781affbc9aa8c60c5cae926517e028f\\kotlin-scripting-compiler-embeddable-2.1.20.jar,D:\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-compiler-impl-embeddable\\2.1.20\\26fd748997fdde79281d4cd4e5dff73a9276067\\kotlin-scripting-compiler-impl-embeddable-2.1.20.jar,D:\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-jvm\\2.1.20\\963b49de86cb5ee7cddf8724187d1658ac85e4ef\\kotlin-scripting-jvm-2.1.20.jar,D:\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-common\\2.1.20\\29a91fb9489edffc21d296b565d9bb62476971b7\\kotlin-scripting-common-2.1.20.jar,D:\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\2.1.20\\aa8ca79cd50578314f6d1180c47cbe14c0fee567\\kotlin-stdlib-2.1.20.jar,D:\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\13.0\\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\\annotations-13.0.jar,D:\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-script-runtime\\2.1.20\\f7c623d7f7bdb01f5ccd6b437bc0a937fcd7c57e\\kotlin-script-runtime-2.1.20.jar" "D:\\my project\\app\\RED AI\\app\\RedAI\\node_modules\\@react-native\\gradle-plugin\\settings-plugin\\src\\main\\kotlin\\com\\facebook\\react\\ReactSettingsExtension.kt" "D:\\my project\\app\\RED AI\\app\\RedAI\\node_modules\\@react-native\\gradle-plugin\\settings-plugin\\src\\main\\kotlin\\com\\facebook\\react\\ReactSettingsPlugin.kt"