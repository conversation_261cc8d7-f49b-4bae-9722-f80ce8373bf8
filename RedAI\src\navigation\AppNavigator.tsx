import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';

// استيراد الشاشات
import SplashScreen from '../screens/SplashScreen';
import WelcomeScreen from '../screens/WelcomeScreen';
import GeneralChatScreen from '../screens/GeneralChatScreen';
import DeveloperChatScreen from '../screens/DeveloperChatScreen';
import WriterChatScreen from '../screens/WriterChatScreen';
import SettingsScreen from '../screens/SettingsScreen';

// استيراد الأنواع
import { RootStackParamList, MainTabParamList } from '../types';
import { useApp } from '../context/AppContext';
import i18nService from '../services/i18nService';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

// التنقل الرئيسي بالتبويبات
const MainTabNavigator = () => {
  const { t } = useTranslation();
  const isRTL = i18nService.isRTL();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'GeneralChat':
              iconName = 'chat';
              break;
            case 'DeveloperChat':
              iconName = 'code';
              break;
            case 'WriterChat':
              iconName = 'edit';
              break;
            case 'Settings':
              iconName = 'settings';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#2196F3',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: '#fff',
          borderTopWidth: 1,
          borderTopColor: '#e0e0e0',
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontFamily: isRTL ? 'Arial' : 'System',
        },
        headerStyle: {
          backgroundColor: '#2196F3',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
          fontFamily: isRTL ? 'Arial' : 'System',
        },
        headerTitleAlign: 'center',
      })}
    >
      <Tab.Screen
        name="GeneralChat"
        component={GeneralChatScreen}
        options={{
          tabBarLabel: t('navigation.generalChat'),
          title: t('chat.general.title'),
        }}
      />
      <Tab.Screen
        name="DeveloperChat"
        component={DeveloperChatScreen}
        options={{
          tabBarLabel: t('navigation.developerChat'),
          title: t('chat.developer.title'),
        }}
      />
      <Tab.Screen
        name="WriterChat"
        component={WriterChatScreen}
        options={{
          tabBarLabel: t('navigation.writerChat'),
          title: t('chat.writer.title'),
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          tabBarLabel: t('navigation.settings'),
          title: t('settings.title'),
        }}
      />
    </Tab.Navigator>
  );
};

// التنقل الرئيسي للتطبيق
const AppNavigator = () => {
  const { state } = useApp();
  const isRTL = i18nService.isRTL();

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: '#2196F3',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
            fontFamily: isRTL ? 'Arial' : 'System',
          },
          headerTitleAlign: 'center',
        }}
      >
        {state.isLoading ? (
          // شاشة التحميل
          <Stack.Screen
            name="Splash"
            component={SplashScreen}
            options={{ headerShown: false }}
          />
        ) : state.isFirstLaunch ? (
          // شاشة الترحيب للمرة الأولى
          <Stack.Screen
            name="Welcome"
            component={WelcomeScreen}
            options={{ headerShown: false }}
          />
        ) : (
          // الشاشات الرئيسية
          <Stack.Screen
            name="Main"
            component={MainTabNavigator}
            options={{ headerShown: false }}
          />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
