# دليل البدء السريع - Red AI

## خطوات سريعة للتشغيل

### 1. تثبيت المكتبات
```bash
cd RedAI
npm install
```

### 2. ربط الأيقونات (Android)
```bash
npx react-native link react-native-vector-icons
```

### 3. تشغيل التطبيق
```bash
# تشغيل Metro
npm start

# في terminal آخر - Android
npm run android

# أو iOS
npm run ios
```

### 4. إعداد مفاتيح API

#### الحصول على Gemini API Key:
1. اذهب إلى: https://makersuite.google.com/app/apikey
2. سجل دخول بحساب Google
3. انقر "Create API Key"
4. انسخ المفتاح

#### الحصول على Serper API Key (اختياري):
1. اذه<PERSON> إلى: https://serper.dev
2. سجل حساب جديد
3. احصل على 2500 بحث مجاني
4. انسخ المفتاح من Dashboard

### 5. إدخال المفاتيح في التطبيق
1. افتح التطبيق
2. اذهب لتبويب "الإعدادات"
3. انقر على "مفتاح Gemini API"
4. الصق المفتاح واضغط "حفظ"
5. كرر للبحث API (اختياري)

## اختبار سريع

### اختبار الدردشة العامة:
- اذهب لتبويب "دردشة عامة"
- اكتب: "مرحبا، كيف حالك؟"
- يجب أن تحصل على رد من الذكاء الاصطناعي

### اختبار مساعد المطورين:
- اذهب لتبويب "مساعد المطورين"
- اكتب: "اشرح لي ما هو React Native"
- يجب أن تحصل على شرح تقني مفصل

### اختبار مساعد الكتاب:
- اذهب لتبويب "مساعد الكتاب"
- اكتب: "اكتب لي قصة قصيرة عن الفضاء"
- يجب أن تحصل على قصة إبداعية

### اختبار البحث:
- في أي تبويب دردشة
- اكتب: "ابحث عن أخبار التكنولوجيا اليوم"
- يجب أن تحصل على نتائج بحث مع رد ذكي

## مشاكل شائعة وحلول سريعة

### المشكلة: التطبيق لا يعمل
**الحل:**
```bash
npx react-native start --reset-cache
```

### المشكلة: خطأ في الأيقونات
**الحل:**
```bash
cd android
./gradlew clean
cd ..
npm run android
```

### المشكلة: لا يوجد رد من الذكاء الاصطناعي
**الحل:**
1. تأكد من إدخال مفتاح Gemini API صحيح
2. تأكد من الاتصال بالإنترنت
3. تحقق من حدود الاستخدام في Google AI Studio

### المشكلة: البحث لا يعمل
**الحل:**
1. تأكد من إدخال مفتاح Serper API
2. تحقق من حدود الاستخدام في Serper.dev
3. البحث اختياري - التطبيق يعمل بدونه

### المشكلة: اللغة العربية لا تظهر بشكل صحيح
**الحل:**
1. تأكد من تفعيل RTL في الإعدادات
2. أعد تشغيل التطبيق
3. تأكد من دعم الجهاز للغة العربية

## نصائح للاستخدام الأمثل

### 1. استخدام مساعد المطورين:
- ضع الكود بين ```
- اطلب شرح مفصل للأكواد
- اطلب مراجعة وتحسين الكود

### 2. استخدام مساعد الكتاب:
- حدد نمط الكتابة المطلوب
- اطلب أفكار قبل البدء بالكتابة
- استخدم التدقيق اللغوي

### 3. استخدام البحث:
- استخدم كلمات مثل "ابحث" أو "ما هو"
- اطلب معلومات حديثة
- اطلب أخبار أو إحصائيات

### 4. إدارة المحادثات:
- استخدم "محادثة جديدة" لموضوع جديد
- امسح المحادثات القديمة لتوفير المساحة
- احفظ المحادثات المهمة بالنسخ

## الدعم والمساعدة

### إذا واجهت مشاكل:
1. راجع ملف README_AR.md للتفاصيل الكاملة
2. راجع ملف DEVELOPER_GUIDE.md للمطورين
3. تأكد من تحديث جميع المكتبات
4. أعد تشغيل Metro Bundler

### للمطورين:
- راجع التعليقات في الكود
- استخدم TypeScript للتطوير الآمن
- اتبع هيكل المجلدات الموجود
- اختبر على أجهزة مختلفة

---

**مبروك! 🎉 تطبيق Red AI جاهز للاستخدام**

استمتع بتجربة الذكاء الاصطناعي المتطور مع دعم كامل للغة العربية!
