{"common": {"ok": "OK", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "send": "Send", "loading": "Loading...", "error": "Error", "success": "Success", "retry": "Retry", "back": "Back", "next": "Next", "start": "Start", "settings": "Settings"}, "welcome": {"title": "Welcome to Red AI", "subtitle": "Your Comprehensive AI Assistant", "description": "Red AI is an advanced intelligent application that provides comprehensive assistance in various fields. Whether you're a developer, writer, or looking for general help, Red AI is here to assist you.", "features": {"general": "General chat with AI", "developer": "Specialized assistant for developers", "writer": "Specialized assistant for writers", "search": "Internet search"}, "getStarted": "Get Started"}, "chat": {"general": {"title": "General <PERSON><PERSON>", "placeholder": "Type your message here...", "emptyState": "Start a new conversation with Red AI"}, "developer": {"title": "Developer Assistant", "placeholder": "Ask about programming, code, or technical issues...", "emptyState": "Hello! I'm your programming assistant. How can I help you today?"}, "writer": {"title": "Writer Assistant", "placeholder": "Ask for help with writing, ideas, or editing...", "emptyState": "Hello! I'm your writing assistant. How can I help you today?"}, "newChat": "New Chat", "clearChat": "Clear Chat", "copyMessage": "Copy Message", "shareMessage": "Share Message"}, "settings": {"title": "Settings", "language": {"title": "Language", "arabic": "العربية", "english": "English"}, "theme": {"title": "Theme", "light": "Light", "dark": "Dark"}, "api": {"title": "API Settings", "geminiKey": "Gemini API Key", "searchKey": "Search API Key", "keyPlaceholder": "Enter API Key", "testKey": "Test Key", "keyValid": "Key is valid", "keyInvalid": "Key is invalid"}, "data": {"title": "Data Management", "clearHistory": "Clear Chat History", "clearHistoryConfirm": "Are you sure you want to clear all conversations?", "export": "Export Data", "import": "Import Data"}, "about": {"title": "About App", "version": "Version", "developer": "Developer", "contact": "Contact"}}, "navigation": {"generalChat": "General <PERSON><PERSON>", "developerChat": "Developer Assistant", "writerChat": "Writer Assistant", "settings": "Settings"}, "errors": {"networkError": "Network connection error", "apiError": "Service error", "invalidApiKey": "Invalid API key", "searchFailed": "Search failed", "generalError": "An unexpected error occurred"}}