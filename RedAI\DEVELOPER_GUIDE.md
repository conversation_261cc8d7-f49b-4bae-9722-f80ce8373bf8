# دليل المطور - Red AI

## نظرة عامة على الكود

تم تطوير تطبيق Red AI باستخدام أفضل الممارسات في React Native مع التركيز على:
- الكود النظيف والقابل للصيانة
- الأداء المحسن
- دعم اللغة العربية (RTL)
- التصميم المتجاوب

## الهيكل المعماري

### 1. إدارة الحالة (State Management)
- استخدام Context API مع useReducer
- حفظ البيانات محلياً باستخدام AsyncStorage
- إدارة جلسات الدردشة والإعدادات

### 2. التنقل (Navigation)
- React Navigation 6 مع Stack و Tab Navigation
- دعم RTL للتنقل
- شاشات ديناميكية حسب حالة التطبيق

### 3. الخدمات (Services)
- **GeminiService**: تكامل مع Gemini API
- **SearchService**: تكامل مع Serper.dev API
- **i18nService**: إدارة الترجمة واللغات

## ملفات مهمة للتعديل

### إضافة مزايا جديدة

#### 1. إضافة شاشة جديدة
```typescript
// src/screens/NewScreen.tsx
import React from 'react';
import { View, Text } from 'react-native';

const NewScreen: React.FC = () => {
  return (
    <View>
      <Text>شاشة جديدة</Text>
    </View>
  );
};

export default NewScreen;
```

#### 2. إضافة التنقل
```typescript
// في src/navigation/AppNavigator.tsx
import NewScreen from '../screens/NewScreen';

// أضف في Tab.Navigator
<Tab.Screen
  name="NewScreen"
  component={NewScreen}
  options={{
    tabBarLabel: 'شاشة جديدة',
    title: 'شاشة جديدة',
  }}
/>
```

#### 3. إضافة ترجمات
```json
// في src/locales/ar.json
{
  "newScreen": {
    "title": "شاشة جديدة",
    "description": "وصف الشاشة الجديدة"
  }
}
```

### تخصيص الألوان والتصميم

#### ملف الألوان المقترح
```typescript
// src/utils/colors.ts
export const Colors = {
  primary: '#2196F3',
  primaryDark: '#1976D2',
  secondary: '#4CAF50',
  accent: '#FF9800',
  background: '#FFFFFF',
  surface: '#F5F5F5',
  text: '#333333',
  textSecondary: '#666666',
  error: '#F44336',
  warning: '#FF9800',
  success: '#4CAF50',
};
```

### إضافة خدمة API جديدة

```typescript
// src/services/newApiService.ts
class NewApiService {
  private apiKey: string = '';
  private baseUrl: string = 'https://api.example.com';

  async callApi(data: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/endpoint`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('API call failed');
      }

      return await response.json();
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  updateApiKey(apiKey: string) {
    this.apiKey = apiKey;
  }
}

export default new NewApiService();
```

## نصائح للتطوير

### 1. إدارة الحالة
- استخدم useApp() hook للوصول للحالة العامة
- أضف actions جديدة في AppContext عند الحاجة
- احفظ البيانات المهمة في AsyncStorage

### 2. التصميم المتجاوب
- استخدم Dimensions للحصول على أبعاد الشاشة
- اختبر على أحجام شاشات مختلفة
- استخدم flexbox للتخطيط

### 3. دعم RTL
- استخدم i18nService.isRTL() للفحص
- اعكس flexDirection عند الحاجة
- استخدم marginLeft/marginRight بشكل شرطي

### 4. الأداء
- استخدم FlatList للقوائم الطويلة
- تجنب re-renders غير الضرورية
- استخدم useMemo و useCallback عند الحاجة

## اختبار التطبيق

### اختبار الوظائف الأساسية
1. تشغيل التطبيق بدون مفاتيح API
2. إدخال مفاتيح API صحيحة
3. اختبار جميع أنواع الدردشة
4. تغيير اللغة والمظهر
5. اختبار حفظ واستعادة البيانات

### اختبار على أجهزة مختلفة
- هواتف بأحجام شاشات مختلفة
- أجهزة لوحية
- اختبار الأداء على أجهزة قديمة

## نشر التطبيق

### Android
```bash
# إنشاء APK للاختبار
cd android
./gradlew assembleRelease

# إنشاء AAB للنشر
./gradlew bundleRelease
```

### iOS
```bash
# في Xcode
# Product > Archive
# ثم اتبع خطوات النشر في App Store Connect
```

## مشاكل شائعة وحلولها

### 1. مشكلة في تحميل الخطوط العربية
```typescript
// أضف في android/app/src/main/assets/fonts/
// ملفات الخطوط العربية المطلوبة
```

### 2. مشكلة في اتجاه النص
```typescript
// تأكد من استخدام writingDirection في TextInput
<TextInput
  style={{
    writingDirection: isRTL ? 'rtl' : 'ltr',
    textAlign: isRTL ? 'right' : 'left',
  }}
/>
```

### 3. مشكلة في حفظ البيانات
```typescript
// تأكد من استخدام JSON.stringify/parse بشكل صحيح
await AsyncStorage.setItem('key', JSON.stringify(data));
const data = JSON.parse(await AsyncStorage.getItem('key') || '{}');
```

## التحديثات المستقبلية المقترحة

1. **إضافة مساعد صوتي**
   - تكامل مع Speech-to-Text
   - تكامل مع Text-to-Speech

2. **إضافة مساعد تعلم اللغات**
   - دروس تفاعلية
   - اختبارات وتمارين

3. **تحسين الأداء**
   - إضافة caching للردود
   - تحسين استهلاك البطارية

4. **إضافة مزايا اجتماعية**
   - مشاركة المحادثات
   - تقييم الردود

5. **إضافة تحليلات**
   - إحصائيات الاستخدام
   - تحليل أداء الذكاء الاصطناعي

## الأمان

### حماية مفاتيح API
- لا تضع مفاتيح API في الكود مباشرة
- استخدم متغيرات البيئة في الإنتاج
- اشفر البيانات الحساسة قبل الحفظ

### حماية البيانات
- اشفر المحادثات المحفوظة محلياً
- امسح البيانات الحساسة عند إلغاء التثبيت
- اطلب إذن المستخدم قبل حفظ البيانات

---

هذا الدليل يوفر نظرة شاملة على كيفية تطوير وتخصيص تطبيق Red AI. للمزيد من المساعدة، راجع التعليقات في الكود أو تواصل مع فريق التطوير.
