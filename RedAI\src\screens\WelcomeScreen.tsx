import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  StatusBar,
  TouchableOpacity,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTranslation } from 'react-i18next';
import { useApp } from '../context/AppContext';
import i18nService from '../services/i18nService';

const { width, height } = Dimensions.get('window');

const WelcomeScreen: React.FC = () => {
  const { t } = useTranslation();
  const { dispatch } = useApp();
  const isRTL = i18nService.isRTL();

  const handleGetStarted = () => {
    dispatch({ type: 'SET_FIRST_LAUNCH', payload: false });
  };

  const features = [
    {
      icon: 'chat',
      title: t('welcome.features.general'),
      color: '#4CAF50',
    },
    {
      icon: 'code',
      title: t('welcome.features.developer'),
      color: '#FF9800',
    },
    {
      icon: 'edit',
      title: t('welcome.features.writer'),
      color: '#9C27B0',
    },
    {
      icon: 'search',
      title: t('welcome.features.search'),
      color: '#F44336',
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1976D2" />
      
      <LinearGradient
        colors={['#2196F3', '#1976D2']}
        style={styles.gradient}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* شعار التطبيق */}
          <Animatable.View
            animation="bounceIn"
            duration={1000}
            style={styles.logoContainer}
          >
            <View style={styles.logoBackground}>
              <Icon name="psychology" size={60} color="#2196F3" />
            </View>
          </Animatable.View>

          {/* العنوان الرئيسي */}
          <Animatable.View
            animation="fadeInUp"
            duration={800}
            delay={300}
            style={styles.titleContainer}
          >
            <Text style={[styles.title, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('welcome.title')}
            </Text>
            <Text style={[styles.subtitle, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('welcome.subtitle')}
            </Text>
          </Animatable.View>

          {/* الوصف */}
          <Animatable.View
            animation="fadeInUp"
            duration={800}
            delay={500}
            style={styles.descriptionContainer}
          >
            <Text style={[styles.description, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('welcome.description')}
            </Text>
          </Animatable.View>

          {/* المزايا */}
          <Animatable.View
            animation="fadeInUp"
            duration={800}
            delay={700}
            style={styles.featuresContainer}
          >
            {features.map((feature, index) => (
              <Animatable.View
                key={index}
                animation="fadeInLeft"
                duration={600}
                delay={900 + index * 100}
                style={[
                  styles.featureItem,
                  { flexDirection: isRTL ? 'row-reverse' : 'row' }
                ]}
              >
                <View style={[styles.featureIcon, { backgroundColor: feature.color }]}>
                  <Icon name={feature.icon} size={24} color="#fff" />
                </View>
                <Text style={[
                  styles.featureText,
                  { 
                    textAlign: isRTL ? 'right' : 'left',
                    marginLeft: isRTL ? 0 : 15,
                    marginRight: isRTL ? 15 : 0,
                  }
                ]}>
                  {feature.title}
                </Text>
              </Animatable.View>
            ))}
          </Animatable.View>

          {/* زر البدء */}
          <Animatable.View
            animation="bounceIn"
            duration={800}
            delay={1300}
            style={styles.buttonContainer}
          >
            <TouchableOpacity
              style={styles.startButton}
              onPress={handleGetStarted}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['#4CAF50', '#45A049']}
                style={styles.buttonGradient}
              >
                <Text style={styles.buttonText}>
                  {t('welcome.getStarted')}
                </Text>
                <Icon 
                  name={isRTL ? "arrow-back" : "arrow-forward"} 
                  size={24} 
                  color="#fff" 
                  style={{ marginLeft: isRTL ? 0 : 10, marginRight: isRTL ? 10 : 0 }}
                />
              </LinearGradient>
            </TouchableOpacity>
          </Animatable.View>
        </ScrollView>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logoBackground: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
  },
  titleContainer: {
    marginBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    fontFamily: 'Arial',
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: 'Arial',
  },
  descriptionContainer: {
    marginBottom: 30,
  },
  description: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 24,
    fontFamily: 'Arial',
  },
  featuresContainer: {
    marginBottom: 40,
  },
  featureItem: {
    alignItems: 'center',
    marginBottom: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 15,
    borderRadius: 12,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
    flex: 1,
    fontFamily: 'Arial',
  },
  buttonContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  startButton: {
    borderRadius: 25,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 15,
  },
  buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    fontFamily: 'Arial',
  },
});

export default WelcomeScreen;
