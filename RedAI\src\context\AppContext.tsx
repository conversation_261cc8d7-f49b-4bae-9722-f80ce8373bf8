import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppSettings, ChatSession, Message } from '../types';

// State interface
interface AppState {
  settings: AppSettings;
  chatSessions: ChatSession[];
  currentSession: ChatSession | null;
  isLoading: boolean;
  isFirstLaunch: boolean;
}

// Actions
type AppAction =
  | { type: 'SET_SETTINGS'; payload: AppSettings }
  | { type: 'SET_CHAT_SESSIONS'; payload: ChatSession[] }
  | { type: 'SET_CURRENT_SESSION'; payload: ChatSession | null }
  | { type: 'ADD_MESSAGE'; payload: { sessionId: string; message: Message } }
  | { type: 'CREATE_SESSION'; payload: ChatSession }
  | { type: 'DELETE_SESSION'; payload: string }
  | { type: 'CLEAR_ALL_SESSIONS' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_FIRST_LAUNCH'; payload: boolean };

// Initial state
const initialState: AppState = {
  settings: {
    language: 'ar',
    theme: 'light',
  },
  chatSessions: [],
  currentSession: null,
  isLoading: true,
  isFirstLaunch: true,
};

// Reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_SETTINGS':
      return { ...state, settings: action.payload };
    case 'SET_CHAT_SESSIONS':
      return { ...state, chatSessions: action.payload };
    case 'SET_CURRENT_SESSION':
      return { ...state, currentSession: action.payload };
    case 'ADD_MESSAGE':
      const updatedSessions = state.chatSessions.map(session =>
        session.id === action.payload.sessionId
          ? { ...session, messages: [...session.messages, action.payload.message], updatedAt: new Date() }
          : session
      );
      const updatedCurrentSession = state.currentSession?.id === action.payload.sessionId
        ? { ...state.currentSession, messages: [...state.currentSession.messages, action.payload.message], updatedAt: new Date() }
        : state.currentSession;
      return { ...state, chatSessions: updatedSessions, currentSession: updatedCurrentSession };
    case 'CREATE_SESSION':
      return { ...state, chatSessions: [...state.chatSessions, action.payload] };
    case 'DELETE_SESSION':
      return {
        ...state,
        chatSessions: state.chatSessions.filter(session => session.id !== action.payload),
        currentSession: state.currentSession?.id === action.payload ? null : state.currentSession,
      };
    case 'CLEAR_ALL_SESSIONS':
      return { ...state, chatSessions: [], currentSession: null };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_FIRST_LAUNCH':
      return { ...state, isFirstLaunch: action.payload };
    default:
      return state;
  }
};

// Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  saveSettings: (settings: AppSettings) => Promise<void>;
  loadData: () => Promise<void>;
  saveChatSessions: () => Promise<void>;
} | undefined>(undefined);

// Provider
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // حفظ الإعدادات
  const saveSettings = async (settings: AppSettings) => {
    try {
      await AsyncStorage.setItem('app_settings', JSON.stringify(settings));
      dispatch({ type: 'SET_SETTINGS', payload: settings });
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  // حفظ جلسات الدردشة
  const saveChatSessions = async () => {
    try {
      await AsyncStorage.setItem('chat_sessions', JSON.stringify(state.chatSessions));
    } catch (error) {
      console.error('Error saving chat sessions:', error);
    }
  };

  // تحميل البيانات
  const loadData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // تحميل الإعدادات
      const settingsData = await AsyncStorage.getItem('app_settings');
      if (settingsData) {
        const settings = JSON.parse(settingsData);
        dispatch({ type: 'SET_SETTINGS', payload: settings });
      }

      // تحميل جلسات الدردشة
      const sessionsData = await AsyncStorage.getItem('chat_sessions');
      if (sessionsData) {
        const sessions = JSON.parse(sessionsData);
        dispatch({ type: 'SET_CHAT_SESSIONS', payload: sessions });
      }

      // فحص إذا كان هذا أول تشغيل
      const firstLaunch = await AsyncStorage.getItem('first_launch');
      if (firstLaunch === null) {
        await AsyncStorage.setItem('first_launch', 'false');
        dispatch({ type: 'SET_FIRST_LAUNCH', payload: true });
      } else {
        dispatch({ type: 'SET_FIRST_LAUNCH', payload: false });
      }

    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // تحميل البيانات عند بدء التطبيق
  useEffect(() => {
    loadData();
  }, []);

  // حفظ جلسات الدردشة عند تغييرها
  useEffect(() => {
    if (!state.isLoading && state.chatSessions.length > 0) {
      saveChatSessions();
    }
  }, [state.chatSessions]);

  return (
    <AppContext.Provider value={{ state, dispatch, saveSettings, loadData, saveChatSessions }}>
      {children}
    </AppContext.Provider>
  );
};

// Hook لاستخدام Context
export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
