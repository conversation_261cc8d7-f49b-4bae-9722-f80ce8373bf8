import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  StatusBar,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useApp } from '../context/AppContext';
import i18nService from '../services/i18nService';

const { width, height } = Dimensions.get('window');

const SplashScreen: React.FC = () => {
  const { loadData } = useApp();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // تحميل اللغة المحفوظة
        await i18nService.loadSavedLanguage();
        
        // تحميل بيانات التطبيق
        await loadData();
        
        // انتظار لمدة ثانيتين لعرض الشاشة
        setTimeout(() => {
          // سيتم التنقل تلقائياً عبر AppNavigator
        }, 2000);
      } catch (error) {
        console.error('Error initializing app:', error);
      }
    };

    initializeApp();
  }, []);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1976D2" />
      
      <LinearGradient
        colors={['#2196F3', '#1976D2', '#0D47A1']}
        style={styles.gradient}
      >
        {/* شعار التطبيق */}
        <Animatable.View
          animation="bounceIn"
          duration={1500}
          style={styles.logoContainer}
        >
          <View style={styles.logoBackground}>
            <Icon name="psychology" size={80} color="#fff" />
          </View>
        </Animatable.View>

        {/* اسم التطبيق */}
        <Animatable.View
          animation="fadeInUp"
          duration={1000}
          delay={500}
          style={styles.titleContainer}
        >
          <Text style={styles.title}>Red AI</Text>
          <Text style={styles.subtitle}>مساعدك الذكي الشامل</Text>
        </Animatable.View>

        {/* مؤشر التحميل */}
        <Animatable.View
          animation="pulse"
          iterationCount="infinite"
          duration={1500}
          style={styles.loadingContainer}
        >
          <View style={styles.loadingDot} />
          <View style={[styles.loadingDot, { marginHorizontal: 8 }]} />
          <View style={styles.loadingDot} />
        </Animatable.View>

        {/* نص التحميل */}
        <Animatable.Text
          animation="fadeIn"
          duration={1000}
          delay={1000}
          style={styles.loadingText}
        >
          جاري التحميل...
        </Animatable.Text>

        {/* معلومات الإصدار */}
        <Animatable.View
          animation="fadeIn"
          duration={1000}
          delay={1500}
          style={styles.versionContainer}
        >
          <Text style={styles.versionText}>الإصدار 1.0.0</Text>
        </Animatable.View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  logoContainer: {
    marginBottom: 40,
  },
  logoBackground: {
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  title: {
    fontSize: 42,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    fontFamily: 'Arial',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  loadingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#fff',
    opacity: 0.7,
  },
  loadingText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    fontFamily: 'Arial',
  },
  versionContainer: {
    position: 'absolute',
    bottom: 40,
    alignItems: 'center',
  },
  versionText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    fontFamily: 'Arial',
  },
});

export default SplashScreen;
