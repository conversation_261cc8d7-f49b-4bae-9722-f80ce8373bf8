import axios from 'axios';
import { SearchResult } from '../types';

// مفتاح API للبحث - يجب وضعه في متغيرات البيئة
const SERPER_API_KEY = '68ede8cd3645595fe01b804165b8d92c5f2e04db'; // يجب استبداله بمفتاح حقيقي

class SearchService {
  private apiKey: string;
  private baseUrl: string = 'https://google.serper.dev/search';

  constructor() {
    this.apiKey = SERPER_API_KEY;
  }

  // البحث في الإنترنت
  async searchWeb(query: string, language: string = 'ar'): Promise<SearchResult[]> {
    try {
      const response = await axios.post(
        this.baseUrl,
        {
          q: query,
          gl: language === 'ar' ? 'sa' : 'us', // السعودية للعربية، أمريكا للإنجليزية
          hl: language,
          num: 5, // عدد النتائج
        },
        {
          headers: {
            'X-API-KEY': this.apiKey,
            'Content-Type': 'application/json',
          },
          timeout: 10000, // 10 ثواني timeout
        }
      );

      if (response.data && response.data.organic) {
        return response.data.organic.map((result: any) => ({
          title: result.title || '',
          snippet: result.snippet || '',
          url: result.link || '',
        }));
      }

      return [];
    } catch (error) {
      console.error('Error searching web:', error);
      throw new Error('فشل في البحث في الإنترنت');
    }
  }

  // البحث مع تحسين للذكاء الاصطناعي
  async searchForAI(query: string, language: string = 'ar'): Promise<string> {
    try {
      const results = await this.searchWeb(query, language);
      
      if (results.length === 0) {
        return 'لم يتم العثور على نتائج للبحث المطلوب.';
      }

      // تنسيق النتائج للذكاء الاصطناعي
      let formattedResults = `نتائج البحث عن "${query}":\n\n`;
      
      results.forEach((result, index) => {
        formattedResults += `${index + 1}. **${result.title}**\n`;
        formattedResults += `   ${result.snippet}\n`;
        formattedResults += `   المصدر: ${result.url}\n\n`;
      });

      return formattedResults;
    } catch (error) {
      console.error('Error in searchForAI:', error);
      return 'حدث خطأ أثناء البحث في الإنترنت.';
    }
  }

  // فحص إذا كان السؤال يحتاج بحث
  shouldSearch(message: string): boolean {
    const searchKeywords = [
      'ابحث', 'بحث', 'ما هو', 'من هو', 'أين', 'متى', 'كيف',
      'أخبار', 'معلومات', 'تعريف', 'شرح', 'وضح',
      'search', 'what is', 'who is', 'where', 'when', 'how',
      'news', 'information', 'definition', 'explain'
    ];

    const lowerMessage = message.toLowerCase();
    return searchKeywords.some(keyword => lowerMessage.includes(keyword.toLowerCase()));
  }

  // تحديث مفتاح API
  updateApiKey(apiKey: string) {
    this.apiKey = apiKey;
  }

  // فحص صحة مفتاح API
  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const response = await axios.post(
        this.baseUrl,
        {
          q: 'test',
          num: 1,
        },
        {
          headers: {
            'X-API-KEY': apiKey,
            'Content-Type': 'application/json',
          },
          timeout: 5000,
        }
      );

      return response.status === 200;
    } catch (error) {
      return false;
    }
  }
}

export default new SearchService();
