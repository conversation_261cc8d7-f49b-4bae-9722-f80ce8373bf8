import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  Alert,
  Text,
  TouchableOpacity,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';
import uuid from 'react-native-uuid';

// استيراد المكونات والخدمات
import ChatMessage from '../components/ChatMessage';
import ChatInput from '../components/ChatInput';
import { useApp } from '../context/AppContext';
import { Message, ChatSession } from '../types';
import geminiService from '../services/geminiService';
import searchService from '../services/searchService';
import i18nService from '../services/i18nService';

const GeneralChatScreen: React.FC = () => {
  const { t } = useTranslation();
  const { state, dispatch } = useApp();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  const flatListRef = useRef<FlatList>(null);
  const isRTL = i18nService.isRTL();

  // إنشاء جلسة جديدة أو تحميل الجلسة الحالية
  useEffect(() => {
    const existingSession = state.chatSessions.find(
      session => session.type === 'general'
    );

    if (existingSession) {
      setCurrentSessionId(existingSession.id);
      setMessages(existingSession.messages);
    } else {
      createNewSession();
    }
  }, []);

  // إنشاء جلسة دردشة جديدة
  const createNewSession = () => {
    const sessionId = uuid.v4() as string;
    const newSession: ChatSession = {
      id: sessionId,
      title: t('chat.general.title'),
      messages: [],
      type: 'general',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    dispatch({ type: 'CREATE_SESSION', payload: newSession });
    setCurrentSessionId(sessionId);
    setMessages([]);
  };

  // إرسال رسالة
  const handleSendMessage = async (messageText: string) => {
    if (!messageText.trim()) return;

    const userMessage: Message = {
      id: uuid.v4() as string,
      text: messageText,
      isUser: true,
      timestamp: new Date(),
      type: 'text',
    };

    // إضافة رسالة المستخدم
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    
    // إضافة الرسالة إلى الجلسة
    dispatch({
      type: 'ADD_MESSAGE',
      payload: { sessionId: currentSessionId, message: userMessage },
    });

    setIsLoading(true);

    try {
      let aiResponse = '';
      let searchResults = '';

      // فحص إذا كان السؤال يحتاج بحث في الإنترنت
      if (searchService.shouldSearch(messageText)) {
        try {
          searchResults = await searchService.searchForAI(
            messageText,
            state.settings.language
          );
        } catch (error) {
          console.warn('Search failed, continuing without search results');
        }
      }

      // الحصول على رد من Gemini
      const context = searchResults ? `نتائج البحث:\n${searchResults}\n\n` : '';
      aiResponse = await geminiService.generateGeneralResponse(messageText, context);

      const aiMessage: Message = {
        id: uuid.v4() as string,
        text: aiResponse,
        isUser: false,
        timestamp: new Date(),
        type: searchResults ? 'search' : 'text',
      };

      // إضافة رد الذكاء الاصطناعي
      const finalMessages = [...updatedMessages, aiMessage];
      setMessages(finalMessages);

      // إضافة الرسالة إلى الجلسة
      dispatch({
        type: 'ADD_MESSAGE',
        payload: { sessionId: currentSessionId, message: aiMessage },
      });

    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert(
        t('common.error'),
        error instanceof Error ? error.message : t('errors.generalError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  // مسح المحادثة
  const handleClearChat = () => {
    Alert.alert(
      t('chat.clearChat'),
      'هل أنت متأكد من مسح هذه المحادثة؟',
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => {
            setMessages([]);
            dispatch({ type: 'DELETE_SESSION', payload: currentSessionId });
            createNewSession();
          },
        },
      ]
    );
  };

  // التمرير إلى أسفل عند إضافة رسالة جديدة
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // رندر رسالة فردية
  const renderMessage = ({ item }: { item: Message }) => (
    <ChatMessage message={item} />
  );

  // رندر الحالة الفارغة
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="chat" size={80} color="#e0e0e0" />
      <Text style={[
        styles.emptyStateText,
        { textAlign: isRTL ? 'right' : 'left' }
      ]}>
        {t('chat.general.emptyState')}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* قائمة الرسائل */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={[
          styles.messagesContent,
          messages.length === 0 && styles.emptyContent,
        ]}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
        onContentSizeChange={() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }}
      />

      {/* شريط الأدوات */}
      {messages.length > 0 && (
        <View style={[
          styles.toolbar,
          { flexDirection: isRTL ? 'row-reverse' : 'row' }
        ]}>
          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={createNewSession}
          >
            <Icon name="add" size={20} color="#2196F3" />
            <Text style={styles.toolbarButtonText}>
              {t('chat.newChat')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={handleClearChat}
          >
            <Icon name="clear" size={20} color="#f44336" />
            <Text style={[styles.toolbarButtonText, { color: '#f44336' }]}>
              {t('chat.clearChat')}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* حقل إدخال الرسائل */}
      <ChatInput
        placeholder={t('chat.general.placeholder')}
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 16,
  },
  emptyContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateText: {
    fontSize: 18,
    color: '#666',
    marginTop: 16,
    fontFamily: 'Arial',
  },
  toolbar: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#f9f9f9',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    justifyContent: 'space-around',
  },
  toolbarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  toolbarButtonText: {
    fontSize: 14,
    color: '#2196F3',
    marginLeft: 4,
    fontFamily: 'Arial',
  },
});

export default GeneralChatScreen;
