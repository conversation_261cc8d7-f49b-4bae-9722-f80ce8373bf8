# Red AI - مساعد ذكي شامل

Red AI هو تطبيق React Native متطور يوفر مساعدة ذكية شاملة في مختلف المجالات باستخدام تقنيات الذكاء الاصطناعي المتقدمة.

## المزايا الرئيسية

- 🤖 **دردشة عامة مع الذكاء الاصطناعي** - مساعد ذكي للأسئلة العامة
- 👨‍💻 **مساعد المطورين** - مساعد متخصص في البرمجة وحل المشاكل التقنية
- ✍️ **مساعد الكتاب** - مساعد متخصص في الكتابة والإبداع الأدبي
- 🔍 **البحث في الإنترنت** - دمج نتائج البحث مع الذكاء الاصطناعي
- 🌐 **دعم متعدد اللغات** - العربية والإنجليزية مع دعم RTL
- 🌙 **الوضع الليلي** - تبديل بين الوضع الفاتح والداكن
- 💾 **العمل بدون إنترنت** - حفظ المحادثات محلياً
- 🎨 **تصميم عصري** - واجهة مستخدم جذابة وسهلة الاستخدام

## التقنيات المستخدمة

- **React Native 0.80.1** - إطار العمل الأساسي
- **TypeScript** - للكتابة الآمنة للكود
- **React Navigation 6** - للتنقل بين الشاشات
- **Context API** - لإدارة الحالة
- **Gemini API** - للذكاء الاصطناعي
- **Serper.dev API** - للبحث في الإنترنت
- **React Native Paper** - مكتبة UI
- **i18next** - للترجمة ودعم اللغات
- **AsyncStorage** - للتخزين المحلي

## متطلبات التشغيل

قبل البدء، تأكد من إعداد بيئة React Native بشكل صحيح:

- Node.js (الإصدار 18 أو أحدث)
- React Native CLI
- Android Studio (للتطوير على Android)
- Xcode (للتطوير على iOS - macOS فقط)

## التثبيت والتشغيل

### 1. تثبيت المكتبات

```bash
cd RedAI
npm install
```

### 2. تكوين مفاتيح API

قبل تشغيل التطبيق، تحتاج إلى الحصول على مفاتيح API:

#### Gemini API Key
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. أنشئ مفتاح API جديد
3. انسخ المفتاح

#### Serper.dev API Key (اختياري للبحث)
1. اذهب إلى [Serper.dev](https://serper.dev)
2. سجل حساب جديد
3. احصل على مفتاح API مجاني

### 3. تشغيل التطبيق

#### لنظام Android:
```bash
# تشغيل Metro Bundler
npm start

# في terminal آخر
npm run android
```

#### لنظام iOS:
```bash
# تشغيل Metro Bundler
npm start

# في terminal آخر
npm run ios
```

## إعداد مفاتيح API في التطبيق

1. افتح التطبيق
2. اذهب إلى صفحة الإعدادات
3. أدخل مفاتيح API في الحقول المخصصة
4. اضغط على "حفظ" لكل مفتاح

## هيكل المشروع

```
RedAI/
├── src/
│   ├── components/          # المكونات المشتركة
│   │   ├── ChatMessage.tsx
│   │   └── ChatInput.tsx
│   ├── screens/            # شاشات التطبيق
│   │   ├── SplashScreen.tsx
│   │   ├── WelcomeScreen.tsx
│   │   ├── GeneralChatScreen.tsx
│   │   ├── DeveloperChatScreen.tsx
│   │   ├── WriterChatScreen.tsx
│   │   └── SettingsScreen.tsx
│   ├── navigation/         # إعدادات التنقل
│   │   └── AppNavigator.tsx
│   ├── context/           # إدارة الحالة
│   │   └── AppContext.tsx
│   ├── services/          # الخدمات والAPI
│   │   ├── geminiService.ts
│   │   ├── searchService.ts
│   │   └── i18nService.ts
│   ├── locales/           # ملفات الترجمة
│   │   ├── ar.json
│   │   └── en.json
│   ├── types/             # تعريفات TypeScript
│   │   └── index.ts
│   └── utils/             # الأدوات المساعدة
├── android/               # ملفات Android
├── ios/                   # ملفات iOS
└── package.json
```

## الاستخدام

### الدردشة العامة
- اطرح أي سؤال عام
- احصل على إجابات ذكية ومفصلة
- البحث التلقائي في الإنترنت للأسئلة التي تتطلب معلومات حديثة

### مساعد المطورين
- اطرح أسئلة برمجية
- احصل على شرح للأكواد
- طلب مراجعة الكود وتحسينه
- حل المشاكل التقنية

### مساعد الكتاب
- طلب أفكار للكتابة
- تحسين النصوص
- كتابة قصص ومقالات
- اختيار أنماط كتابة مختلفة

## التخصيص والتطوير

### إضافة مزايا جديدة
1. أنشئ شاشة جديدة في مجلد `screens/`
2. أضف التنقل في `AppNavigator.tsx`
3. أضف الترجمات في ملفات `locales/`
4. حدث الأنواع في `types/index.ts`

### تخصيص التصميم
- عدل الألوان في ملفات الـ styles
- أضف مكونات UI جديدة في مجلد `components/`
- استخدم React Native Paper للمكونات الجاهزة

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في Metro Bundler**
   ```bash
   npx react-native start --reset-cache
   ```

2. **مشاكل في Android**
   ```bash
   cd android
   ./gradlew clean
   cd ..
   npm run android
   ```

3. **مشاكل في مفاتيح API**
   - تأكد من صحة المفاتيح
   - تحقق من الاتصال بالإنترنت
   - راجع حدود الاستخدام للAPI

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التغييرات مع التعليقات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## التواصل

- **المطور**: Red AI Team
- **الإصدار**: 1.0.0
- **التاريخ**: 2024

---

**ملاحظة**: هذا التطبيق يتطلب مفاتيح API للعمل بشكل كامل. تأكد من الحصول على المفاتيح المطلوبة قبل الاستخدام.
